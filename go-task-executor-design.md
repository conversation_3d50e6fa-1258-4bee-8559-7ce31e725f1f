# Go任务执行器技术方案

## 1. 项目概述

基于当前Spug项目的任务执行模块，使用Go语言重新实现一个轻量级的任务执行器，主要包含模版管理和任务执行功能，并在原有功能基础上增加以下特性：

- 自定义超时时间控制
- 自定义并发度控制  
- 自定义暂停机器功能
- 任务执行历史查询

## 2. 系统架构

### 2.1 整体架构
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Web API       │    │  Task Scheduler │    │  Task Executor  │
│   (Gin/Echo)    │◄──►│   (Cron Jobs)   │◄──►│   (Workers)     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Database      │    │     Redis       │    │   SSH Client    │
│  (PostgreSQL)   │    │   (Queue/Cache) │    │   (Remote Exec) │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 2.2 核心模块

#### 2.2.1 模版管理模块 (Template Manager)
- 模版CRUD操作
- 模版分类管理
- 参数化模版支持
- 模版版本控制

#### 2.2.2 任务执行模块 (Task Executor)
- 实时任务执行
- 批量主机执行
- 执行状态监控
- 结果实时推送

#### 2.2.3 调度器模块 (Scheduler)
- 定时任务调度
- 触发器管理
- 任务队列管理

#### 2.2.4 并发控制模块 (Concurrency Controller)
- 全局并发度控制
- 主机级别并发控制
- 任务优先级管理

#### 2.2.5 超时控制模块 (Timeout Controller)
- 任务级别超时
- 主机级别超时
- 自定义超时策略

#### 2.2.6 主机管理模块 (Host Manager)
- 主机信息管理
- SSH连接池
- 主机状态监控
- 暂停/恢复机制

## 3. 数据模型设计

### 3.1 模版表 (templates)
```sql
CREATE TABLE templates (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    type VARCHAR(50) NOT NULL,
    body TEXT NOT NULL,
    interpreter VARCHAR(20) DEFAULT 'sh',
    parameters JSONB DEFAULT '[]',
    host_ids JSONB DEFAULT '[]',
    timeout_seconds INTEGER DEFAULT 3600,
    description TEXT,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    created_by INTEGER,
    updated_by INTEGER
);
```

### 3.2 主机表 (hosts)
```sql
CREATE TABLE hosts (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    hostname VARCHAR(255) NOT NULL,
    port INTEGER DEFAULT 22,
    username VARCHAR(100) NOT NULL,
    private_key TEXT,
    password VARCHAR(255),
    is_active BOOLEAN DEFAULT true,
    is_paused BOOLEAN DEFAULT false,
    max_concurrent INTEGER DEFAULT 5,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);
```

### 3.3 任务表 (tasks)
```sql
CREATE TABLE tasks (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    type VARCHAR(50) NOT NULL,
    template_id INTEGER REFERENCES templates(id),
    interpreter VARCHAR(20) DEFAULT 'sh',
    command TEXT NOT NULL,
    targets JSONB NOT NULL,
    trigger_type VARCHAR(20),
    trigger_args JSONB,
    timeout_seconds INTEGER DEFAULT 3600,
    max_concurrent INTEGER DEFAULT 10,
    is_active BOOLEAN DEFAULT false,
    description TEXT,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    created_by INTEGER
);
```

### 3.4 执行历史表 (execution_histories)
```sql
CREATE TABLE execution_histories (
    id SERIAL PRIMARY KEY,
    task_id INTEGER REFERENCES tasks(id),
    template_id INTEGER REFERENCES templates(id),
    digest VARCHAR(64) UNIQUE NOT NULL,
    interpreter VARCHAR(20) NOT NULL,
    command TEXT NOT NULL,
    parameters JSONB DEFAULT '{}',
    host_ids JSONB NOT NULL,
    status VARCHAR(20) DEFAULT 'pending',
    start_time TIMESTAMP,
    end_time TIMESTAMP,
    duration_seconds REAL,
    timeout_seconds INTEGER,
    max_concurrent INTEGER,
    created_at TIMESTAMP DEFAULT NOW(),
    created_by INTEGER
);
```

### 3.5 主机执行结果表 (host_execution_results)
```sql
CREATE TABLE host_execution_results (
    id SERIAL PRIMARY KEY,
    history_id INTEGER REFERENCES execution_histories(id),
    host_id INTEGER REFERENCES hosts(id),
    status VARCHAR(20) DEFAULT 'pending',
    exit_code INTEGER,
    output TEXT,
    error_output TEXT,
    start_time TIMESTAMP,
    end_time TIMESTAMP,
    duration_seconds REAL,
    created_at TIMESTAMP DEFAULT NOW()
);
```

## 4. 核心功能实现

### 4.1 Go项目结构
```
task-executor/
├── cmd/
│   ├── server/          # Web服务器入口
│   ├── worker/          # 工作进程入口
│   └── scheduler/       # 调度器入口
├── internal/
│   ├── api/            # HTTP API处理
│   ├── config/         # 配置管理
│   ├── database/       # 数据库操作
│   ├── executor/       # 任务执行器
│   ├── models/         # 数据模型
│   ├── scheduler/      # 任务调度器
│   ├── ssh/           # SSH客户端
│   └── websocket/     # WebSocket实时通信
├── pkg/
│   ├── concurrency/   # 并发控制
│   ├── timeout/       # 超时控制
│   └── utils/         # 工具函数
├── configs/           # 配置文件
├── migrations/        # 数据库迁移
└── docs/             # 文档
```

### 4.2 关键技术组件

#### 4.2.1 并发控制器
```go
type ConcurrencyController struct {
    globalSemaphore   *semaphore.Weighted
    hostSemaphores    map[int]*semaphore.Weighted
    maxGlobalWorkers  int64
    hostMaxWorkers    map[int]int64
    mutex            sync.RWMutex
}

func (cc *ConcurrencyController) AcquireGlobal(ctx context.Context) error {
    return cc.globalSemaphore.Acquire(ctx, 1)
}

func (cc *ConcurrencyController) AcquireHost(ctx context.Context, hostID int) error {
    cc.mutex.RLock()
    sem, exists := cc.hostSemaphores[hostID]
    cc.mutex.RUnlock()
    
    if !exists {
        return fmt.Errorf("host %d not found", hostID)
    }
    return sem.Acquire(ctx, 1)
}
```

#### 4.2.2 超时控制器
```go
type TimeoutController struct {
    defaultTimeout time.Duration
    taskTimeouts   map[string]time.Duration
    hostTimeouts   map[int]time.Duration
    mutex         sync.RWMutex
}

func (tc *TimeoutController) GetTimeout(taskID string, hostID int) time.Duration {
    tc.mutex.RLock()
    defer tc.mutex.RUnlock()
    
    if timeout, exists := tc.taskTimeouts[taskID]; exists {
        return timeout
    }
    if timeout, exists := tc.hostTimeouts[hostID]; exists {
        return timeout
    }
    return tc.defaultTimeout
}
```

#### 4.2.3 主机暂停控制器
```go
type HostController struct {
    pausedHosts map[int]bool
    mutex      sync.RWMutex
}

func (hc *HostController) PauseHost(hostID int) {
    hc.mutex.Lock()
    defer hc.mutex.Unlock()
    hc.pausedHosts[hostID] = true
}

func (hc *HostController) ResumeHost(hostID int) {
    hc.mutex.Lock()
    defer hc.mutex.Unlock()
    delete(hc.pausedHosts, hostID)
}

func (hc *HostController) IsHostPaused(hostID int) bool {
    hc.mutex.RLock()
    defer hc.mutex.RUnlock()
    return hc.pausedHosts[hostID]
}
```
